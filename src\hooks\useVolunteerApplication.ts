import { useMutation, useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { volunteerApi, VolunteerApplicationData, VolunteerApplicationResponse } from '../apis/volunteer';
import { useErrorHandler, ErrorTracker } from '../utils/errorHandler';

// Hook for submitting volunteer application
export const useVolunteerApplication = () => {
  const { handleError } = useErrorHandler();
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async (data: VolunteerApplicationData): Promise<VolunteerApplicationResponse> => {
      ErrorTracker.getInstance().trackUserAction('volunteer_application_attempt', {
        roles: data.selectedRoles,
        hasExperience: data.hasVolunteerExperience
      });

      const result = await volunteerApi.submitApplication(data);
      
      ErrorTracker.getInstance().trackUserAction('volunteer_application_success', {
        applicationId: result.id,
        roles: data.selectedRoles
      });

      return result;
    },
    onSuccess: (data) => {
      toast.success('Volunteer application submitted successfully! We will review your application and contact you soon.', {
        duration: 6000,
        position: 'top-center',
      });

      // Redirect to a success page or back to volunteer opportunities
      navigate('/volunteer/opportunities', { 
        state: { 
          applicationSubmitted: true,
          applicationId: data.id 
        }
      });
    },
    onError: (error) => {
      handleError(error as Error, {
        component: 'useVolunteerApplication',
        action: 'submit_application',
      });

      toast.error('Failed to submit volunteer application. Please try again.', {
        duration: 5000,
        position: 'top-center',
      });
    },
    retry: (failureCount, error) => {
      // Don't retry on validation errors
      const errorMessage = error?.message?.toLowerCase() || '';
      if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for getting user's volunteer applications
export const useMyVolunteerApplications = () => {
  const { handleError } = useErrorHandler();

  return useQuery({
    queryKey: ['volunteerApplications', 'my'],
    queryFn: volunteerApi.getMyApplications,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      const errorMessage = error?.message?.toLowerCase() || '';
      if (errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
        return false;
      }
      return failureCount < 3;
    },
    onError: (error) => {
      handleError(error as Error, {
        component: 'useMyVolunteerApplications',
        action: 'fetch_applications',
      });
    }
  });
};

// Hook for getting all volunteer applications (admin only)
export const useAllVolunteerApplications = (page: number = 1, pageSize: number = 10) => {
  const { handleError } = useErrorHandler();

  return useQuery({
    queryKey: ['volunteerApplications', 'all', page, pageSize],
    queryFn: () => volunteerApi.getAllApplications(page, pageSize),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication/authorization errors
      const errorMessage = error?.message?.toLowerCase() || '';
      if (errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
        return false;
      }
      return failureCount < 3;
    },
    onError: (error) => {
      handleError(error as Error, {
        component: 'useAllVolunteerApplications',
        action: 'fetch_all_applications',
      });
    }
  });
};

// Hook for updating application status (admin only)
export const useUpdateApplicationStatus = () => {
  const { handleError } = useErrorHandler();

  return useMutation({
    mutationFn: async ({ applicationId, status }: { applicationId: string; status: 'approved' | 'rejected' }) => {
      ErrorTracker.getInstance().trackUserAction('volunteer_application_status_update', {
        applicationId,
        status
      });

      return await volunteerApi.updateApplicationStatus(applicationId, status);
    },
    onSuccess: (data, variables) => {
      const statusText = variables.status === 'approved' ? 'approved' : 'rejected';
      toast.success(`Volunteer application ${statusText} successfully!`, {
        duration: 4000,
        position: 'top-center',
      });

      ErrorTracker.getInstance().trackUserAction('volunteer_application_status_update_success', {
        applicationId: variables.applicationId,
        status: variables.status
      });
    },
    onError: (error, variables) => {
      handleError(error as Error, {
        component: 'useUpdateApplicationStatus',
        action: 'update_status',
        additionalData: { applicationId: variables.applicationId, status: variables.status }
      });

      toast.error('Failed to update application status. Please try again.', {
        duration: 5000,
        position: 'top-center',
      });
    },
    retry: (failureCount, error) => {
      // Don't retry on authentication/authorization errors
      const errorMessage = error?.message?.toLowerCase() || '';
      if (errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
        return false;
      }
      return failureCount < 2;
    }
  });
};

// Hook for getting volunteer roles
export const useVolunteerRoles = () => {
  return useQuery({
    queryKey: ['volunteerRoles'],
    queryFn: volunteerApi.getRoles,
    staleTime: 30 * 60 * 1000, // 30 minutes - roles don't change often
    cacheTime: 60 * 60 * 1000, // 1 hour
  });
};
