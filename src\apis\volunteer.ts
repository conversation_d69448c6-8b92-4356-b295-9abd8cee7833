import { apiClient } from './client';

// Volunteer Application Interfaces
export interface VolunteerRole {
  id: string;
  name: string;
  description: string;
  requirements: string[];
  timeCommitment: string;
  urgency: 'Low' | 'Medium' | 'High';
}

export interface VolunteerApplicationData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  dateOfBirth: string;
  
  // Emergency Contact
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  
  // Volunteer Information
  selectedRoles: string[];
  availability: string[];
  skills: string[];
  experience: string;
  motivation: string;
  hasVolunteerExperience: boolean;
  volunteerExperienceDetails?: string;
  
  // Preferences
  preferredLocation: string;
  languagesSpoken: string[];
  
  // Legal
  agreeToTerms: boolean;
  agreeToBackgroundCheck: boolean;
  
  // Additional
  additionalComments?: string;
}

export interface VolunteerApplicationResponse {
  id: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  message: string;
}

export interface VolunteerApplicationListResponse {
  applications: VolunteerApplicationResponse[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

// Predefined volunteer roles
export const VOLUNTEER_ROLES: VolunteerRole[] = [
  {
    id: 'emergency-coordinator',
    name: 'Emergency Response Coordinator',
    description: 'Coordinate emergency response activities and manage volunteer teams during disasters.',
    requirements: ['Leadership experience', 'Crisis management skills', 'Communication skills'],
    timeCommitment: '4-8 hours/week',
    urgency: 'High'
  },
  {
    id: 'community-liaison',
    name: 'Community Liaison Officer',
    description: 'Act as liaison between disaster response teams and local communities.',
    requirements: ['Communication skills', 'Community relations experience'],
    timeCommitment: '3-6 hours/week',
    urgency: 'Medium'
  },
  {
    id: 'relief-distribution',
    name: 'Relief Distribution Manager',
    description: 'Oversee relief supply distribution and coordinate with logistics teams.',
    requirements: ['Organization skills', 'Logistics experience', 'Leadership'],
    timeCommitment: '6-10 hours/week',
    urgency: 'High'
  },
  {
    id: 'information-management',
    name: 'Information Management Specialist',
    description: 'Manage disaster information systems and coordinate data collection.',
    requirements: ['Technology skills', 'Data management experience'],
    timeCommitment: '4-8 hours/week',
    urgency: 'Medium'
  },
  {
    id: 'first-aid',
    name: 'First Aid Responder',
    description: 'Provide immediate medical assistance and first aid during emergencies.',
    requirements: ['First aid certification', 'Medical training'],
    timeCommitment: '4-12 hours/week',
    urgency: 'High'
  },
  {
    id: 'shelter-support',
    name: 'Shelter Support Volunteer',
    description: 'Assist in setting up and managing emergency shelters for displaced individuals.',
    requirements: ['Organization skills', 'Compassion', 'Physical ability'],
    timeCommitment: '6-12 hours/week',
    urgency: 'Medium'
  },
  {
    id: 'communication',
    name: 'Communication Specialist',
    description: 'Handle public communications, social media, and information dissemination.',
    requirements: ['Communication skills', 'Social media experience', 'Writing skills'],
    timeCommitment: '3-8 hours/week',
    urgency: 'Medium'
  },
  {
    id: 'general-support',
    name: 'General Support Volunteer',
    description: 'Provide general assistance across various disaster response activities as needed.',
    requirements: ['Flexibility', 'Willingness to help', 'Basic communication skills'],
    timeCommitment: '2-6 hours/week',
    urgency: 'Low'
  }
];

// Availability options
export const AVAILABILITY_OPTIONS = [
  { value: 'weekdays-morning', label: 'Weekdays (Morning: 6 AM - 12 PM)' },
  { value: 'weekdays-afternoon', label: 'Weekdays (Afternoon: 12 PM - 6 PM)' },
  { value: 'weekdays-evening', label: 'Weekdays (Evening: 6 PM - 10 PM)' },
  { value: 'weekends-morning', label: 'Weekends (Morning: 6 AM - 12 PM)' },
  { value: 'weekends-afternoon', label: 'Weekends (Afternoon: 12 PM - 6 PM)' },
  { value: 'weekends-evening', label: 'Weekends (Evening: 6 PM - 10 PM)' },
  { value: 'emergency-only', label: 'Emergency Response Only (24/7 on-call)' },
  { value: 'flexible', label: 'Flexible Schedule' }
];

// Skills options
export const SKILLS_OPTIONS = [
  'Leadership',
  'Communication',
  'Crisis Management',
  'First Aid/Medical',
  'Technology/IT',
  'Data Management',
  'Logistics',
  'Organization',
  'Community Relations',
  'Social Media',
  'Writing',
  'Translation',
  'Construction',
  'Transportation',
  'Counseling',
  'Education/Training',
  'Finance/Accounting',
  'Legal',
  'Photography/Documentation',
  'Food Service',
  'Other'
];

// Languages options
export const LANGUAGES_OPTIONS = [
  'English',
  'Myanmar (Burmese)',
  'Shan',
  'Karen',
  'Rakhine',
  'Mon',
  'Chin',
  'Kachin',
  'Kayah',
  'Chinese',
  'Thai',
  'Hindi',
  'Other'
];

// Volunteer API service
export const volunteerApi = {
  // Submit volunteer application
  submitApplication: async (data: VolunteerApplicationData): Promise<VolunteerApplicationResponse> => {
    const response = await apiClient.post('/Volunteer/applications', data);
    return response.data;
  },

  // Get user's volunteer applications
  getMyApplications: async (): Promise<VolunteerApplicationListResponse> => {
    const response = await apiClient.get('/Volunteer/applications/my');
    return response.data;
  },

  // Get all volunteer applications (admin only)
  getAllApplications: async (page: number = 1, pageSize: number = 10): Promise<VolunteerApplicationListResponse> => {
    const response = await apiClient.get('/Volunteer/applications', {
      params: { page, pageSize }
    });
    return response.data;
  },

  // Update application status (admin only)
  updateApplicationStatus: async (applicationId: string, status: 'approved' | 'rejected'): Promise<VolunteerApplicationResponse> => {
    const response = await apiClient.put(`/Volunteer/applications/${applicationId}/status`, { status });
    return response.data;
  },

  // Get volunteer roles
  getRoles: async (): Promise<VolunteerRole[]> => {
    // For now, return predefined roles. In the future, this could be from the backend
    return Promise.resolve(VOLUNTEER_ROLES);
  }
};
