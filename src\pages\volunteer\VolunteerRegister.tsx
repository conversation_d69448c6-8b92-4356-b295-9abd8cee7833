import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Heart, ArrowLeft, Users, CheckCircle, Mail, Phone, Shield, MapPin, Clock, Star, User, FileText } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useVolunteerApplication, useVolunteerRoles } from '../../hooks/useVolunteerApplication';
import { VolunteerApplicationData, AVAILABILITY_OPTIONS, SKILLS_OPTIONS, LANGUAGES_OPTIONS } from '../../apis/volunteer';
import LoadingSpinner from '../../components/LoadingSpinner';
import Header from '../../components/Layout/Header';

const VolunteerRegister: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const { data: volunteerRoles, isLoading: rolesLoading } = useVolunteerRoles();
  const volunteerApplicationMutation = useVolunteerApplication();

  // Form data state
  const [formData, setFormData] = useState<VolunteerApplicationData>({
    // Personal Information
    firstName: '',
    lastName: '',
    email: user?.email || '',
    phone: '',
    address: '',
    dateOfBirth: '',

    // Emergency Contact
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',

    // Volunteer Information
    selectedRoles: [],
    availability: [],
    skills: [],
    experience: '',
    motivation: '',
    hasVolunteerExperience: false,
    volunteerExperienceDetails: '',

    // Preferences
    preferredLocation: '',
    languagesSpoken: ['English'],

    // Legal
    agreeToTerms: false,
    agreeToBackgroundCheck: false,

    // Additional
    additionalComments: ''
  });

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  // Update email when user data is available
  useEffect(() => {
    if (user?.email && !formData.email) {
      setFormData(prev => ({ ...prev, email: user.email }));
    }
  }, [user, formData.email]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (name: keyof VolunteerApplicationData, value: string) => {
    setFormData(prev => {
      const currentValues = prev[name] as string[];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      return { ...prev, [name]: newValues };
    });
  };

  // Validate current step
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1: // Personal Information
        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
        if (!formData.email.trim()) newErrors.email = 'Email is required';
        if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
        if (!formData.address.trim()) newErrors.address = 'Address is required';
        if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
        break;

      case 2: // Emergency Contact & Volunteer Info
        if (!formData.emergencyContactName.trim()) newErrors.emergencyContactName = 'Emergency contact name is required';
        if (!formData.emergencyContactPhone.trim()) newErrors.emergencyContactPhone = 'Emergency contact phone is required';
        if (!formData.emergencyContactRelationship.trim()) newErrors.emergencyContactRelationship = 'Relationship is required';
        if (formData.selectedRoles.length === 0) newErrors.selectedRoles = 'Please select at least one volunteer role';
        if (formData.availability.length === 0) newErrors.availability = 'Please select your availability';
        break;

      case 3: // Skills & Experience
        if (formData.skills.length === 0) newErrors.skills = 'Please select at least one skill';
        if (!formData.motivation.trim()) newErrors.motivation = 'Please explain your motivation';
        if (formData.hasVolunteerExperience && !formData.volunteerExperienceDetails?.trim()) {
          newErrors.volunteerExperienceDetails = 'Please provide details about your volunteer experience';
        }
        break;

      case 4: // Final Details & Agreement
        if (!formData.agreeToTerms) newErrors.agreeToTerms = 'You must agree to the terms and conditions';
        if (!formData.agreeToBackgroundCheck) newErrors.agreeToBackgroundCheck = 'You must agree to the background check';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle next step
  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  // Handle previous step
  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateStep(currentStep)) {
      volunteerApplicationMutation.mutate(formData);
    }
  };

  // Show loading spinner while roles are loading
  if (rolesLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading volunteer roles..." />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <Users size={32} />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-4">
            Please Login to Continue
          </h3>
          <p className="text-gray-600 mb-8">
            You need to be logged in to submit a volunteer application.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/login"
              state={{ from: { pathname: "/volunteer/register" } }}
              className="bg-blue-600 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200"
            >
              Login to Continue
            </Link>
            <Link
              to="/signup"
              className="bg-gray-200 text-gray-700 px-8 py-3 rounded-xl font-semibold hover:bg-gray-300 transition-colors duration-200"
            >
              Create Account
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Header />
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-16 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-blue-600/5"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white text-sm font-semibold mb-6">
            <Heart size={16} className="mr-2 text-red-400" />
            Volunteer Application
          </div>

          <h1 className="text-4xl sm:text-5xl font-black text-white mb-6 leading-tight">
            Join Our Emergency
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-blue-200 to-indigo-300">
              Response Team
            </span>
          </h1>

          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
            Complete your volunteer application to join our dedicated team of disaster response volunteers.
          </p>

          <Link
            to="/volunteer"
            className="inline-flex items-center text-blue-200 hover:text-white transition-colors duration-300"
          >
            <ArrowLeft size={20} className="mr-2" />
            Back to Volunteer Info
          </Link>
        </div>
      </section>

      {/* Application Form */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${
                    step <= currentStep
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {step < currentStep ? <CheckCircle size={20} /> : step}
                  </div>
                  {step < totalSteps && (
                    <div className={`flex-1 h-1 mx-4 ${
                      step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="text-center">
              <span className="text-sm text-gray-600">
                Step {currentStep} of {totalSteps}
              </span>
            </div>
          </div>

          {/* Form Card */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <form onSubmit={handleSubmit}>
              <div className="p-8">
                {/* Step 1: Personal Information */}
                {currentStep === 1 && (
                  <div>
                    <div className="mb-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                        <User size={24} className="mr-3 text-blue-600" />
                        Personal Information
                      </h2>
                      <p className="text-gray-600">
                        Please provide your basic personal information.
                      </p>
                    </div>

                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            First Name *
                          </label>
                          <input
                            type="text"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleInputChange}
                            className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                              errors.firstName ? 'border-red-300' : 'border-gray-200'
                            }`}
                            placeholder="Enter your first name"
                          />
                          {errors.firstName && (
                            <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Last Name *
                          </label>
                          <input
                            type="text"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleInputChange}
                            className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                              errors.lastName ? 'border-red-300' : 'border-gray-200'
                            }`}
                            placeholder="Enter your last name"
                          />
                          {errors.lastName && (
                            <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Address *
                          </label>
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                              errors.email ? 'border-red-300' : 'border-gray-200'
                            }`}
                            placeholder="Enter your email address"
                          />
                          {errors.email && (
                            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number *
                          </label>
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                              errors.phone ? 'border-red-300' : 'border-gray-200'
                            }`}
                            placeholder="Enter your phone number"
                          />
                          {errors.phone && (
                            <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Address *
                        </label>
                        <textarea
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          rows={3}
                          className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                            errors.address ? 'border-red-300' : 'border-gray-200'
                          }`}
                          placeholder="Enter your full address"
                        />
                        {errors.address && (
                          <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Date of Birth *
                        </label>
                        <input
                          type="date"
                          name="dateOfBirth"
                          value={formData.dateOfBirth}
                          onChange={handleInputChange}
                          className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                            errors.dateOfBirth ? 'border-red-300' : 'border-gray-200'
                          }`}
                        />
                        {errors.dateOfBirth && (
                          <p className="text-red-500 text-sm mt-1">{errors.dateOfBirth}</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Emergency Contact & Volunteer Roles */}
                {currentStep === 2 && (
                  <div>
                    <div className="mb-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                        <Shield size={24} className="mr-3 text-blue-600" />
                        Emergency Contact & Volunteer Roles
                      </h2>
                      <p className="text-gray-600">
                        Provide emergency contact information and select your preferred volunteer roles.
                      </p>
                    </div>

                    <div className="space-y-8">
                      {/* Emergency Contact Section */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Phone size={18} className="mr-2 text-blue-600" />
                          Emergency Contact
                        </h3>

                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Contact Name *
                              </label>
                              <input
                                type="text"
                                name="emergencyContactName"
                                value={formData.emergencyContactName}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                                  errors.emergencyContactName ? 'border-red-300' : 'border-gray-200'
                                }`}
                                placeholder="Emergency contact name"
                              />
                              {errors.emergencyContactName && (
                                <p className="text-red-500 text-sm mt-1">{errors.emergencyContactName}</p>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Contact Phone *
                              </label>
                              <input
                                type="tel"
                                name="emergencyContactPhone"
                                value={formData.emergencyContactPhone}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                                  errors.emergencyContactPhone ? 'border-red-300' : 'border-gray-200'
                                }`}
                                placeholder="Emergency contact phone"
                              />
                              {errors.emergencyContactPhone && (
                                <p className="text-red-500 text-sm mt-1">{errors.emergencyContactPhone}</p>
                              )}
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Relationship *
                            </label>
                            <select
                              name="emergencyContactRelationship"
                              value={formData.emergencyContactRelationship}
                              onChange={handleInputChange}
                              className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                                errors.emergencyContactRelationship ? 'border-red-300' : 'border-gray-200'
                              }`}
                            >
                              <option value="">Select relationship</option>
                              <option value="parent">Parent</option>
                              <option value="spouse">Spouse</option>
                              <option value="sibling">Sibling</option>
                              <option value="child">Child</option>
                              <option value="friend">Friend</option>
                              <option value="other">Other</option>
                            </select>
                            {errors.emergencyContactRelationship && (
                              <p className="text-red-500 text-sm mt-1">{errors.emergencyContactRelationship}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Volunteer Roles Section */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Star size={18} className="mr-2 text-blue-600" />
                          Volunteer Roles *
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          Select one or more volunteer roles you're interested in:
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {volunteerRoles?.map((role) => (
                            <div
                              key={role.id}
                              className={`border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                                formData.selectedRoles.includes(role.id)
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-blue-300'
                              }`}
                              onClick={() => handleMultiSelectChange('selectedRoles', role.id)}
                            >
                              <div className="flex items-start">
                                <input
                                  type="checkbox"
                                  checked={formData.selectedRoles.includes(role.id)}
                                  onChange={() => handleMultiSelectChange('selectedRoles', role.id)}
                                  className="mt-1 mr-3 text-blue-600 focus:ring-blue-500"
                                />
                                <div className="flex-1">
                                  <h4 className="font-semibold text-gray-900 mb-1">{role.name}</h4>
                                  <p className="text-sm text-gray-600 mb-2">{role.description}</p>
                                  <div className="flex items-center text-xs text-gray-500">
                                    <Clock size={12} className="mr-1" />
                                    {role.timeCommitment}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        {errors.selectedRoles && (
                          <p className="text-red-500 text-sm mt-2">{errors.selectedRoles}</p>
                        )}
                      </div>

                      {/* Availability Section */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Clock size={18} className="mr-2 text-blue-600" />
                          Availability *
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          Select your available time slots:
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {AVAILABILITY_OPTIONS.map((option) => (
                            <label
                              key={option.value}
                              className={`flex items-center p-3 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                                formData.availability.includes(option.value)
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-blue-300'
                              }`}
                            >
                              <input
                                type="checkbox"
                                checked={formData.availability.includes(option.value)}
                                onChange={() => handleMultiSelectChange('availability', option.value)}
                                className="mr-3 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm font-medium text-gray-700">{option.label}</span>
                            </label>
                          ))}
                        </div>
                        {errors.availability && (
                          <p className="text-red-500 text-sm mt-2">{errors.availability}</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3: Skills & Experience */}
                {currentStep === 3 && (
                  <div>
                    <div className="mb-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                        <Star size={24} className="mr-3 text-blue-600" />
                        Skills & Experience
                      </h2>
                      <p className="text-gray-600">
                        Tell us about your skills and experience to help us match you with suitable volunteer opportunities.
                      </p>
                    </div>

                    <div className="space-y-6">
                      {/* Skills Section */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                          Skills *
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          Select all skills that apply to you:
                        </p>

                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {SKILLS_OPTIONS.map((skill) => (
                            <label
                              key={skill}
                              className={`flex items-center p-3 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                                formData.skills.includes(skill)
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-blue-300'
                              }`}
                            >
                              <input
                                type="checkbox"
                                checked={formData.skills.includes(skill)}
                                onChange={() => handleMultiSelectChange('skills', skill)}
                                className="mr-3 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm font-medium text-gray-700">{skill}</span>
                            </label>
                          ))}
                        </div>
                        {errors.skills && (
                          <p className="text-red-500 text-sm mt-2">{errors.skills}</p>
                        )}
                      </div>

                      {/* Experience Section */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Relevant Experience
                        </label>
                        <textarea
                          name="experience"
                          value={formData.experience}
                          onChange={handleInputChange}
                          rows={4}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200"
                          placeholder="Describe any relevant work, volunteer, or life experience..."
                        />
                      </div>

                      {/* Motivation Section */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Why do you want to volunteer? *
                        </label>
                        <textarea
                          name="motivation"
                          value={formData.motivation}
                          onChange={handleInputChange}
                          rows={4}
                          className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                            errors.motivation ? 'border-red-300' : 'border-gray-200'
                          }`}
                          placeholder="Tell us what motivates you to volunteer for disaster response..."
                        />
                        {errors.motivation && (
                          <p className="text-red-500 text-sm mt-1">{errors.motivation}</p>
                        )}
                      </div>

                      {/* Previous Volunteer Experience */}
                      <div>
                        <label className="flex items-center mb-4">
                          <input
                            type="checkbox"
                            name="hasVolunteerExperience"
                            checked={formData.hasVolunteerExperience}
                            onChange={handleInputChange}
                            className="mr-3 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm font-medium text-gray-700">
                            I have previous volunteer experience
                          </span>
                        </label>

                        {formData.hasVolunteerExperience && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Please describe your volunteer experience *
                            </label>
                            <textarea
                              name="volunteerExperienceDetails"
                              value={formData.volunteerExperienceDetails || ''}
                              onChange={handleInputChange}
                              rows={3}
                              className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ${
                                errors.volunteerExperienceDetails ? 'border-red-300' : 'border-gray-200'
                              }`}
                              placeholder="Describe your previous volunteer experience..."
                            />
                            {errors.volunteerExperienceDetails && (
                              <p className="text-red-500 text-sm mt-1">{errors.volunteerExperienceDetails}</p>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Languages */}
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                          Languages Spoken
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          Select all languages you can speak:
                        </p>

                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {LANGUAGES_OPTIONS.map((language) => (
                            <label
                              key={language}
                              className={`flex items-center p-3 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                                formData.languagesSpoken.includes(language)
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-blue-300'
                              }`}
                            >
                              <input
                                type="checkbox"
                                checked={formData.languagesSpoken.includes(language)}
                                onChange={() => handleMultiSelectChange('languagesSpoken', language)}
                                className="mr-3 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm font-medium text-gray-700">{language}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 4: Final Details & Agreement */}
                {currentStep === 4 && (
                  <div>
                    <div className="mb-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                        <FileText size={24} className="mr-3 text-blue-600" />
                        Final Details & Agreement
                      </h2>
                      <p className="text-gray-600">
                        Complete your application with final details and required agreements.
                      </p>
                    </div>

                    <div className="space-y-6">
                      {/* Preferred Location */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Preferred Location/Area
                        </label>
                        <input
                          type="text"
                          name="preferredLocation"
                          value={formData.preferredLocation}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200"
                          placeholder="e.g., Yangon, Mandalay, or any specific area"
                        />
                      </div>

                      {/* Additional Comments */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Additional Comments
                        </label>
                        <textarea
                          name="additionalComments"
                          value={formData.additionalComments || ''}
                          onChange={handleInputChange}
                          rows={4}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200"
                          placeholder="Any additional information you'd like to share..."
                        />
                      </div>

                      {/* Legal Agreements */}
                      <div className="bg-gray-50 rounded-xl p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                          Required Agreements
                        </h3>

                        <div className="space-y-4">
                          <label className="flex items-start">
                            <input
                              type="checkbox"
                              name="agreeToTerms"
                              checked={formData.agreeToTerms}
                              onChange={handleInputChange}
                              className="mr-3 mt-1 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700">
                              I agree to the <Link to="/terms" className="text-blue-600 hover:underline">terms and conditions</Link> of the volunteer program and understand my responsibilities as a disaster response volunteer. *
                            </span>
                          </label>
                          {errors.agreeToTerms && (
                            <p className="text-red-500 text-sm">{errors.agreeToTerms}</p>
                          )}

                          <label className="flex items-start">
                            <input
                              type="checkbox"
                              name="agreeToBackgroundCheck"
                              checked={formData.agreeToBackgroundCheck}
                              onChange={handleInputChange}
                              className="mr-3 mt-1 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700">
                              I consent to a background check as required for volunteer positions involving emergency response and community safety. *
                            </span>
                          </label>
                          {errors.agreeToBackgroundCheck && (
                            <p className="text-red-500 text-sm">{errors.agreeToBackgroundCheck}</p>
                          )}
                        </div>
                      </div>

                      {/* Application Summary */}
                      <div className="bg-blue-50 rounded-xl p-6">
                        <h3 className="text-lg font-semibold text-blue-900 mb-4">
                          Application Summary
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-blue-800">Name:</span>
                            <span className="ml-2 text-blue-700">{formData.firstName} {formData.lastName}</span>
                          </div>
                          <div>
                            <span className="font-medium text-blue-800">Email:</span>
                            <span className="ml-2 text-blue-700">{formData.email}</span>
                          </div>
                          <div>
                            <span className="font-medium text-blue-800">Selected Roles:</span>
                            <span className="ml-2 text-blue-700">
                              {formData.selectedRoles.length > 0
                                ? volunteerRoles?.filter(role => formData.selectedRoles.includes(role.id))
                                    .map(role => role.name).join(', ')
                                : 'None selected'
                              }
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-blue-800">Skills:</span>
                            <span className="ml-2 text-blue-700">
                              {formData.skills.length > 0 ? formData.skills.slice(0, 3).join(', ') + (formData.skills.length > 3 ? '...' : '') : 'None selected'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={handlePrevStep}
                    disabled={currentStep === 1}
                    className={`px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${
                      currentStep === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    Previous
                  </button>

                  {currentStep < totalSteps ? (
                    <button
                      type="button"
                      onClick={handleNextStep}
                      className="bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200"
                    >
                      Next Step
                    </button>
                  ) : (
                    <button
                      type="submit"
                      disabled={volunteerApplicationMutation.isPending}
                      className="bg-blue-600 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      {volunteerApplicationMutation.isPending ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Heart size={18} className="mr-2" />
                          Submit Application
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      </section>
    </div>
  );
};

export default VolunteerRegister;