import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Heart, Users, Shield, ArrowRight, CheckCircle, Clock, MapPin, Star } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { VOLUNTEER_ROLES } from '../../apis/volunteer';

const VolunteerPage: React.FC = () => {
  const { isAuthenticated } = useAuth();

  const stats = [
    { label: 'Active Volunteers', value: '1,200+', icon: Users },
    { label: 'Lives Impacted', value: '15,000+', icon: Heart },
    { label: 'Emergency Responses', value: '350+', icon: Shield },
    { label: 'Communities Served', value: '85+', icon: MapPin }
  ];

  const benefits = [
    'Make a meaningful difference in disaster response',
    'Gain valuable emergency management experience',
    'Connect with like-minded community members',
    'Receive professional training and certification',
    'Flexible scheduling to fit your availability',
    'Be part of a dedicated emergency response team'
  ];

  const featuredRoles = VOLUNTEER_ROLES.slice(0, 4); // Show first 4 roles

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-blue-600/5"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white text-sm font-semibold mb-6">
              <Heart size={16} className="mr-2 text-red-400" />
              Join Our Emergency Response Team
            </div>

            <h1 className="text-5xl sm:text-6xl font-black text-white mb-6 leading-tight">
              Become a
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-blue-200 to-indigo-300">
                Disaster Response Volunteer
              </span>
            </h1>

            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Join our dedicated team of volunteers and make a real difference in disaster preparedness, 
              response, and recovery efforts in your community.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to={isAuthenticated ? "/volunteer/register" : "/login"}
                state={!isAuthenticated ? { from: { pathname: "/volunteer/register" } } : undefined}
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center"
              >
                <Heart size={20} className="mr-2" />
                Apply to Volunteer
              </Link>
              <Link
                to="/volunteer/opportunities"
                className="bg-blue-500/20 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-500/30 transition-all duration-300 border border-white/20 flex items-center justify-center"
              >
                View Opportunities
                <ArrowRight size={20} className="ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <stat.icon size={32} />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Roles Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Volunteer Opportunities
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose from various roles that match your skills and availability. 
              Every contribution makes a difference in our disaster response efforts.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {featuredRoles.map((role) => (
              <div key={role.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-xl hover:border-blue-200 transition-all duration-300 overflow-hidden">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-bold text-gray-900 leading-tight">
                      {role.name}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold border ${
                      role.urgency === 'High' 
                        ? 'bg-red-50 text-red-700 border-red-200'
                        : role.urgency === 'Medium'
                        ? 'bg-yellow-50 text-yellow-700 border-yellow-200'
                        : 'bg-green-50 text-green-700 border-green-200'
                    }`}>
                      {role.urgency}
                    </span>
                  </div>

                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {role.description}
                  </p>

                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <Clock size={14} className="mr-1" />
                    {role.timeCommitment}
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Requirements:</h4>
                    <div className="flex flex-wrap gap-1">
                      {role.requirements.slice(0, 3).map((req, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                          {req}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Link
              to="/volunteer/opportunities"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-300"
            >
              View All Opportunities
              <ArrowRight size={18} className="ml-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Why Volunteer With Us?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Volunteering with our disaster response team offers meaningful opportunities 
                to serve your community while developing valuable skills and connections.
              </p>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start">
                    <CheckCircle size={20} className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl p-8 text-white">
                <div className="flex items-center mb-6">
                  <Star size={24} className="text-yellow-400 mr-2" />
                  <h3 className="text-xl font-bold">Ready to Get Started?</h3>
                </div>
                
                <p className="text-blue-100 mb-6">
                  Join our community of dedicated volunteers and start making a difference today. 
                  The application process is simple and we provide all necessary training.
                </p>
                
                <Link
                  to={isAuthenticated ? "/volunteer/register" : "/login"}
                  state={!isAuthenticated ? { from: { pathname: "/volunteer/register" } } : undefined}
                  className="bg-white text-blue-600 px-6 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors duration-300 inline-flex items-center"
                >
                  <Heart size={18} className="mr-2" />
                  Start Your Application
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default VolunteerPage;
