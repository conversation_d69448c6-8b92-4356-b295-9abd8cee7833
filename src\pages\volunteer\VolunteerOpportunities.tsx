import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, ArrowLeft, MapPin, Clock, Heart, Award, AlertTriangle, Lock, Shield, User, Mail, Phone, CheckCircle, UserPlus } from 'lucide-react';
import Header from '../../components/Layout/Header';
import Footer from '../../components/Layout/Footer';
import { useAuth } from '../../hooks/useAuth';
import { useRoles } from '../../hooks/useRoles';
import { useVolunteerApplication, useVolunteerRoles } from '../../hooks/useVolunteerApplication';
import { VolunteerApplicationData, AVAILABILITY_OPTIONS, SKILLS_OPTIONS, LANGUAGES_OPTIONS } from '../../apis/volunteer';
import LoadingSpinner from '../../components/LoadingSpinner';

const VolunteerOpportunities: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const { isCj } = useRoles();
  const { data: volunteerRoles, isLoading: rolesLoading } = useVolunteerRoles();
  const volunteerApplicationMutation = useVolunteerApplication();

  // Form data state
  const [formData, setFormData] = useState<VolunteerApplicationData>({
    // Personal Information
    firstName: '',
    lastName: '',
    email: user?.email || '',
    phone: '',
    address: '',
    dateOfBirth: '',

    // Emergency Contact
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',

    // Volunteer Information
    selectedRoles: [],
    availability: [],
    skills: [],
    experience: '',
    motivation: '',
    hasVolunteerExperience: false,
    volunteerExperienceDetails: '',

    // Preferences
    preferredLocation: '',
    languagesSpoken: ['English'],

    // Legal
    agreeToTerms: false,
    agreeToBackgroundCheck: false,

    // Additional
    additionalComments: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const opportunities = [
    {
      id: 1,
      title: "Emergency Response Coordinator",
      location: "Citywide",
      timeCommitment: "4-8 hours/week",
      urgency: "High",
      description: "Coordinate emergency response activities and manage volunteer teams during disasters.",
      skills: ["Leadership", "Communication", "Crisis Management"],
      needed: 5
    },
    {
      id: 2,
      title: "Community Liaison Officer",
      location: "Local Communities",
      timeCommitment: "3-6 hours/week",
      urgency: "Medium",
      description: "Act as liaison between disaster response teams and local communities.",
      skills: ["Communication", "Community Relations"],
      needed: 8
    },
    {
      id: 3,
      title: "Relief Distribution Manager",
      location: "Distribution Centers",
      timeCommitment: "6-10 hours/week",
      urgency: "High",
      description: "Oversee relief supply distribution and coordinate with logistics teams.",
      skills: ["Organization", "Logistics", "Leadership"],
      needed: 3
    },
    {
      id: 4,
      title: "Information Management Specialist",
      location: "Command Center",
      timeCommitment: "4-8 hours/week",
      urgency: "Medium",
      description: "Manage disaster information systems and coordinate data collection.",
      skills: ["Technology", "Data Management"],
      needed: 4
    }
  ];

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'High': return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const stats = [
    { value: "24", label: "Active CJ Volunteers", icon: Users },
    { value: "12", label: "Communities", icon: MapPin },
    { value: "24/7", label: "Response Ready", icon: Clock },
    { value: "100%", label: "CJ Coverage", icon: Shield }
  ];

  // Check if user has CJ role access
  const hasAccess = isAuthenticated && isCj();

  // Form handlers
  const handleInputChange = (field: keyof VolunteerApplicationData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleArrayChange = (field: keyof VolunteerApplicationData, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
        ? [...(prev[field] as string[]), value]
        : (prev[field] as string[]).filter(item => item !== value)
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
    if (!formData.emergencyContactName.trim()) newErrors.emergencyContactName = 'Emergency contact name is required';
    if (!formData.emergencyContactPhone.trim()) newErrors.emergencyContactPhone = 'Emergency contact phone is required';
    if (!formData.emergencyContactRelationship.trim()) newErrors.emergencyContactRelationship = 'Emergency contact relationship is required';
    if (formData.selectedRoles.length === 0) newErrors.selectedRoles = 'Please select at least one volunteer role';
    if (formData.availability.length === 0) newErrors.availability = 'Please select your availability';
    if (formData.skills.length === 0) newErrors.skills = 'Please select your skills';
    if (!formData.motivation.trim()) newErrors.motivation = 'Please explain your motivation';
    if (!formData.agreeToTerms) newErrors.agreeToTerms = 'You must agree to the terms and conditions';
    if (!formData.agreeToBackgroundCheck) newErrors.agreeToBackgroundCheck = 'You must agree to the background check';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await volunteerApplicationMutation.mutateAsync(formData);
      setShowSuccessMessage(true);
      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: user?.email || '',
        phone: '',
        address: '',
        dateOfBirth: '',
        emergencyContactName: '',
        emergencyContactPhone: '',
        emergencyContactRelationship: '',
        selectedRoles: [],
        availability: [],
        skills: [],
        experience: '',
        motivation: '',
        hasVolunteerExperience: false,
        volunteerExperienceDetails: '',
        preferredLocation: '',
        languagesSpoken: ['English'],
        agreeToTerms: false,
        agreeToBackgroundCheck: false,
        additionalComments: ''
      });
    } catch (error) {
      console.error('Error submitting application:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/20">
      <Header />

      <main className="pt-0">
        {/* Hero Section - About Us Inspired Theme */}
        <section className="py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 relative overflow-hidden">
          {/* Clean gradient overlay like About Us */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-800/20 to-blue-900/40"></div>

          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Main Title - Clean and Bold like About Us */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-8 leading-tight tracking-tight">
              Volunteer Opportunities
            </h1>

            {/* Subtitle - Clean and Professional */}
            <p className="text-xl sm:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
              Join our emergency response team and make a meaningful impact in disaster management and community support
            </p>

            {/* Action Buttons - Clean Design like About Us */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {hasAccess ? (
                <>
                  <button
                    onClick={() => document.getElementById('volunteer-opportunities')?.scrollIntoView({ behavior: 'smooth' })}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center justify-center shadow-lg"
                  >
                    <Users size={20} className="mr-2" />
                    View Opportunities
                  </button>
                  <Link
                    to="/volunteer/register"
                    className="bg-transparent border-2 border-white/30 hover:border-white hover:bg-white/10 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center justify-center"
                  >
                    <Heart size={20} className="mr-2" />
                    Apply Now
                  </Link>
                </>
              ) : (
                <>
                  <button
                    onClick={() => document.getElementById('volunteer-form')?.scrollIntoView({ behavior: 'smooth' })}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center justify-center shadow-lg"
                  >
                    <Heart size={20} className="mr-2" />
                    Get Involved
                  </button>
                  <Link
                    to="/volunteer"
                    className="bg-transparent border-2 border-white/30 hover:border-white hover:bg-white/10 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 flex items-center justify-center"
                  >
                    <Users size={20} className="mr-2" />
                    Learn More
                  </Link>
                </>
              )}
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-12">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            {!hasAccess ? (
              /* Volunteer Registration Form */
              <div id="volunteer-form" className="max-w-4xl mx-auto">
                {showSuccessMessage ? (
                  <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                    <div className="p-8 text-center">
                      <div className="w-16 h-16 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <CheckCircle size={32} />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">
                        Application Submitted Successfully!
                      </h3>
                      <p className="text-gray-600 mb-8">
                        Thank you for your interest in volunteering. We'll review your application and contact you soon.
                      </p>
                      <Link
                        to="/"
                        className="bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200"
                      >
                        Return to Home
                      </Link>
                    </div>
                  </div>
                ) : (
                  <div className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
                    {/* Refined Header - About Us Inspired */}
                    <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 px-8 py-8">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                          <UserPlus size={32} className="text-white" />
                        </div>
                        <h2 className="text-3xl font-bold text-white mb-2">
                          Join Our Team
                        </h2>
                        <p className="text-blue-100 text-lg">Register as a Community Justice Volunteer</p>
                      </div>
                    </div>

                    <form onSubmit={handleSubmit} className="p-8">
                      {/* Personal Information */}
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <User size={18} className="mr-2 text-blue-600" />
                          Personal Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              First Name *
                            </label>
                            <input
                              type="text"
                              value={formData.firstName}
                              onChange={(e) => handleInputChange('firstName', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.firstName ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Enter your first name"
                            />
                            {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Last Name *
                            </label>
                            <input
                              type="text"
                              value={formData.lastName}
                              onChange={(e) => handleInputChange('lastName', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.lastName ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Enter your last name"
                            />
                            {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Email *
                            </label>
                            <input
                              type="email"
                              value={formData.email}
                              onChange={(e) => handleInputChange('email', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.email ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Enter your email"
                            />
                            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Phone Number *
                            </label>
                            <input
                              type="tel"
                              value={formData.phone}
                              onChange={(e) => handleInputChange('phone', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.phone ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Enter your phone number"
                            />
                            {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                          </div>
                          <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Address *
                            </label>
                            <input
                              type="text"
                              value={formData.address}
                              onChange={(e) => handleInputChange('address', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.address ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Enter your full address"
                            />
                            {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Date of Birth *
                            </label>
                            <input
                              type="date"
                              value={formData.dateOfBirth}
                              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.dateOfBirth ? 'border-red-500' : 'border-gray-300'
                              }`}
                            />
                            {errors.dateOfBirth && <p className="text-red-500 text-sm mt-1">{errors.dateOfBirth}</p>}
                          </div>
                        </div>
                      </div>

                      {/* Emergency Contact */}
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Phone size={18} className="mr-2 text-blue-600" />
                          Emergency Contact
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Contact Name *
                            </label>
                            <input
                              type="text"
                              value={formData.emergencyContactName}
                              onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.emergencyContactName ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Emergency contact name"
                            />
                            {errors.emergencyContactName && <p className="text-red-500 text-sm mt-1">{errors.emergencyContactName}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Contact Phone *
                            </label>
                            <input
                              type="tel"
                              value={formData.emergencyContactPhone}
                              onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.emergencyContactPhone ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Emergency contact phone"
                            />
                            {errors.emergencyContactPhone && <p className="text-red-500 text-sm mt-1">{errors.emergencyContactPhone}</p>}
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Relationship *
                            </label>
                            <input
                              type="text"
                              value={formData.emergencyContactRelationship}
                              onChange={(e) => handleInputChange('emergencyContactRelationship', e.target.value)}
                              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                                errors.emergencyContactRelationship ? 'border-red-500' : 'border-gray-300'
                              }`}
                              placeholder="Relationship (e.g., spouse, parent)"
                            />
                            {errors.emergencyContactRelationship && <p className="text-red-500 text-sm mt-1">{errors.emergencyContactRelationship}</p>}
                          </div>
                        </div>
                      </div>

                      {/* Volunteer Roles */}
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Shield size={18} className="mr-2 text-blue-600" />
                          Volunteer Roles *
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {rolesLoading ? (
                            <div className="col-span-2 flex justify-center py-4">
                              <LoadingSpinner />
                            </div>
                          ) : (
                            volunteerRoles?.map((role) => (
                              <label key={role.id} className="flex items-start space-x-3 p-4 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-colors cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={formData.selectedRoles.includes(role.id)}
                                  onChange={(e) => handleArrayChange('selectedRoles', role.id, e.target.checked)}
                                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <div>
                                  <div className="font-medium text-gray-900">{role.name}</div>
                                  <div className="text-sm text-gray-600">{role.description}</div>
                                </div>
                              </label>
                            ))
                          )}
                        </div>
                        {errors.selectedRoles && <p className="text-red-500 text-sm mt-2">{errors.selectedRoles}</p>}
                      </div>

                      {/* Availability */}
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Clock size={18} className="mr-2 text-blue-600" />
                          Availability *
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {AVAILABILITY_OPTIONS.map((option) => (
                            <label key={option.value} className="flex items-center space-x-2 p-3 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-colors cursor-pointer">
                              <input
                                type="checkbox"
                                checked={formData.availability.includes(option.value)}
                                onChange={(e) => handleArrayChange('availability', option.value, e.target.checked)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="text-sm font-medium text-gray-900">{option.label}</span>
                            </label>
                          ))}
                        </div>
                        {errors.availability && <p className="text-red-500 text-sm mt-2">{errors.availability}</p>}
                      </div>

                      {/* Skills */}
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                          <Award size={18} className="mr-2 text-blue-600" />
                          Skills & Expertise *
                        </h3>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          {SKILLS_OPTIONS.map((skill) => (
                            <label key={skill} className="flex items-center space-x-2 p-3 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-colors cursor-pointer">
                              <input
                                type="checkbox"
                                checked={formData.skills.includes(skill)}
                                onChange={(e) => handleArrayChange('skills', skill, e.target.checked)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="text-sm font-medium text-gray-900">{skill}</span>
                            </label>
                          ))}
                        </div>
                        {errors.skills && <p className="text-red-500 text-sm mt-2">{errors.skills}</p>}
                      </div>

                      {/* Motivation */}
                      <div className="mb-8">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">
                          Why do you want to volunteer? *
                        </h3>
                        <textarea
                          value={formData.motivation}
                          onChange={(e) => handleInputChange('motivation', e.target.value)}
                          rows={4}
                          className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                            errors.motivation ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="Tell us about your motivation to volunteer and how you want to help your community..."
                        />
                        {errors.motivation && <p className="text-red-500 text-sm mt-1">{errors.motivation}</p>}
                      </div>

                      {/* Terms and Conditions */}
                      <div className="mb-8">
                        <div className="space-y-4">
                          <label className="flex items-start space-x-3">
                            <input
                              type="checkbox"
                              checked={formData.agreeToTerms}
                              onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="text-sm text-gray-700">
                              I agree to the <Link to="/terms" className="text-blue-600 hover:text-blue-700 underline">terms and conditions</Link> and volunteer policies. *
                            </span>
                          </label>
                          {errors.agreeToTerms && <p className="text-red-500 text-sm">{errors.agreeToTerms}</p>}

                          <label className="flex items-start space-x-3">
                            <input
                              type="checkbox"
                              checked={formData.agreeToBackgroundCheck}
                              onChange={(e) => handleInputChange('agreeToBackgroundCheck', e.target.checked)}
                              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="text-sm text-gray-700">
                              I consent to a background check as required for volunteer positions. *
                            </span>
                          </label>
                          {errors.agreeToBackgroundCheck && <p className="text-red-500 text-sm">{errors.agreeToBackgroundCheck}</p>}
                        </div>
                      </div>

                      {/* Submit Button */}
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                        >
                          {isSubmitting ? (
                            <>
                              <LoadingSpinner />
                              <span className="ml-2">Submitting...</span>
                            </>
                          ) : (
                            <>
                              <Heart size={20} className="mr-2" />
                              Submit Application
                            </>
                          )}
                        </button>
                        <Link
                          to="/"
                          className="bg-gray-200 text-gray-700 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-300 transition-colors duration-300 flex items-center justify-center"
                        >
                          <ArrowLeft size={20} className="mr-2" />
                          Back to Home
                        </Link>
                      </div>
                    </form>
                  </div>
                )}
              </div>
            ) : (
              /* Opportunities for CJ Users - About Us Inspired Design */
              <div id="volunteer-opportunities">
                <div className="text-center mb-16">
                  <h2 className="text-4xl font-bold text-gray-900 mb-6">
                    Current Volunteer Opportunities
                  </h2>
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Specialized emergency response roles designed for Community Justice volunteers.
                    Make a meaningful impact in disaster management and community support.
                  </p>
                </div>

                {/* Opportunities Grid - Refined About Us Style */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                  {opportunities.map((opportunity) => (
                    <div key={opportunity.id} className="bg-white rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl hover:border-blue-200 transition-all duration-500 overflow-hidden group">
                      {/* Card Header with Blue Theme */}
                      <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 p-6 text-white">
                        <div className="flex items-center justify-between mb-3">
                          <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                            <opportunity.icon size={24} className="text-white" />
                          </div>
                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                            opportunity.urgency === 'High' ? 'bg-red-500 text-white' :
                            opportunity.urgency === 'Medium' ? 'bg-yellow-500 text-white' :
                            'bg-green-500 text-white'
                          }`}>
                            {opportunity.urgency} Priority
                          </span>
                        </div>
                        <h3 className="text-xl font-bold leading-tight">
                          {opportunity.title}
                        </h3>
                      </div>

                      {/* Card Content */}
                      <div className="p-6">

                        <p className="text-gray-600 mb-6 leading-relaxed">
                          {opportunity.description}
                        </p>

                        {/* Details */}
                        <div className="space-y-3 mb-6">
                          <div className="flex items-center text-sm text-gray-600">
                            <MapPin size={16} className="text-blue-600 mr-3" />
                            <span className="font-medium">{opportunity.location}</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Clock size={16} className="text-blue-600 mr-3" />
                            <span className="font-medium">{opportunity.timeCommitment}</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Users size={16} className="text-blue-600 mr-3" />
                            <span className="font-medium">{opportunity.needed} positions needed</span>
                          </div>
                        </div>

                        {/* Skills */}
                        <div className="mb-6">
                          <h4 className="text-sm font-bold text-gray-900 mb-3">Required Skills:</h4>
                          <div className="flex flex-wrap gap-2">
                            {opportunity.skills.map((skill, index) => (
                              <span key={index} className="px-3 py-1 bg-blue-50 text-blue-700 text-xs font-semibold rounded-full border border-blue-200">
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>

                        {/* Apply Button */}
                        <Link
                          to="/volunteer/register"
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-xl font-bold transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl"
                        >
                          <Heart size={18} className="mr-2" />
                          Apply Now
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Call to Action - About Us Inspired */}
                <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 rounded-3xl p-12 text-center text-white shadow-2xl">
                  <div className="max-w-3xl mx-auto">
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                      <Shield size={40} className="text-white" />
                    </div>
                    <h3 className="text-3xl font-bold mb-6">
                      Ready to Make a Difference?
                    </h3>
                    <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                      Join our Community Justice emergency response team and help protect your community
                      during critical moments. Your skills and dedication can save lives.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Link
                        to="/volunteer/register"
                        className="bg-white text-blue-700 px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-50 transition-all duration-300 inline-flex items-center justify-center shadow-lg hover:shadow-xl"
                      >
                        <Heart size={20} className="mr-2" />
                        Apply Now
                      </Link>
                      <Link
                        to="/contact"
                        className="bg-blue-500/20 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-500/30 transition-all duration-300 border border-white/20 inline-flex items-center justify-center"
                      >
                        <Users size={20} className="mr-2" />
                        Contact Us
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Benefits Section - Only for CJ Users */}
        {hasAccess && (
          <section className="py-12 bg-gradient-to-br from-gray-50 to-blue-50">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  CJ Volunteer Benefits
                </h2>
                <p className="text-lg text-gray-600">
                  Specialized training and leadership opportunities
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white rounded-2xl p-6 shadow-lg border border-blue-100 text-center hover:shadow-xl transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <Shield size={24} className="text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Leadership Training</h3>
                  <p className="text-gray-600">
                    Advanced emergency response leadership and coordination training.
                  </p>
                </div>

                <div className="bg-white rounded-2xl p-6 shadow-lg border border-blue-100 text-center hover:shadow-xl transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <Users size={24} className="text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Network Access</h3>
                  <p className="text-gray-600">
                    Connect with emergency response professionals and community leaders.
                  </p>
                </div>

                <div className="bg-white rounded-2xl p-6 shadow-lg border border-blue-100 text-center hover:shadow-xl transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mb-4 mx-auto">
                    <Award size={24} className="text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Recognition</h3>
                  <p className="text-gray-600">
                    Official recognition and certification for emergency response service.
                  </p>
                </div>
              </div>
            </div>
          </section>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default VolunteerOpportunities;